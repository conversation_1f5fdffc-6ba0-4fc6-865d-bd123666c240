# Sub-Tools 🎬

Набор простых веб-инструментов для работы с субтитрами в формате SRT.

## 🛠️ Инструменты

### 1. Subtitle Cleaner
**Ссылка:** [https://xxphantom.github.io/sub-tools/sub-cleaner.html](https://xxphantom.github.io/sub-tools/sub-cleaner.html)

Двунаправленный инструмент для работы с таймингами субтитров:

- **Функция 1:** Убрать тайминги из SRT файла, оставив только номера блоков и текст
- **Функция 2:** Добавить тайминги обратно к отредактированному тексту

**Применение:**
1. Загрузите оригинальный SRT файл
2. Получите чистый текст для редактирования/перевода
3. Отредактируйте текст, сохранив структуру блоков
4. Восстановите SRT с оригинальными таймингами

### 2. SRT to Plain Text
**Ссылка:** [https://xxphantom.github.io/sub-tools/sub-splitter.html](https://xxphantom.github.io/sub-tools/sub-splitter.html)

Конвертер SRT субтитров в сплошной текст:

- Удаляет номера блоков и тайминги
- Убирает HTML теги
- Объединяет все предложения в единый текст
- Автоматически выделяет результат для копирования

**Применение:**
- Извлечение текста из субтитров для анализа
- Подготовка текста для переводчиков
- Создание транскриптов из субтитров

## 🚀 Использование

Все инструменты работают полностью в браузере, не требуют установки и не отправляют данные на сервер.

1. Перейдите по ссылке на нужный инструмент
2. Вставьте ваш SRT файл в соответствующее поле
3. Нажмите кнопку обработки
4. Скопируйте результат

## 📝 Формат SRT

Инструменты работают со стандартным форматом SRT:

```
1
00:00:01,000 --> 00:00:03,000
Первая строка субтитров

2
00:00:04,000 --> 00:00:06,000
Вторая строка субтитров
```

## 🔧 Технические детали

- Чистый HTML/CSS/JavaScript
- Работает офлайн после загрузки
- Поддерживает различные кодировки
- Обрабатывает HTML теги в субтитрах

## 📄 Лицензия

MIT License
